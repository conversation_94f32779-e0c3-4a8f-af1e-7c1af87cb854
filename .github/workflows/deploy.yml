name: Deploy the Official Site
on:
  push:
    branches:
      - main

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: "20"

      - name: Build the site
        run: npm install -g pnpm && pnpm i && pnpm vitepress build

      - name: Deploy to EdgeOne Page
        uses: TencentEdgeOne/edgeone-pages-action@v1
        with:
          token: ${{ secrets.EDGEONE_API_TOKEN }}
          project-name: "zcs-site"
          build-path: "./.vitepress/dist"
