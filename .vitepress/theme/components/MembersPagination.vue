<template>
  <div class="members-pagination">
    <!-- 搜索框 -->
    <div class="search-box">
      <input
        v-model="searchQuery"
        type="text"
        placeholder="搜索成员昵称或所属组别..."
        class="search-input"
      />
    </div>

    <!-- 筛选选项 -->
    <div class="filter-box">
      <div class="filter-item">
        <label>按组别筛选：</label>
        <select v-model="selectedGroup" class="group-filter">
          <option value="">全部</option>
          <option v-for="group in uniqueGroups" :key="group" :value="group">
            {{ group }}
          </option>
        </select>
      </div>
      
      <div class="filter-item">
        <label>每页显示：</label>
        <select v-model="pageSize" class="page-size-selector">
          <option :value="10">10</option>
          <option :value="20">20</option>
          <option :value="30">30</option>
        </select>
      </div>
    </div>

    <!-- 成员统计 -->
    <div class="stats">
      <span>共 {{ filteredMembers.length }} 位成员</span>
      <span v-if="searchQuery || selectedGroup">
        (已筛选，总共 {{ membersData.length }} 位)
      </span>
    </div>

    <!-- 顶部分页控件 -->
    <div v-if="totalPages > 1" class="pagination pagination-top">
      <button
        @click="currentPage = 1"
        :disabled="currentPage === 1"
        class="page-btn"
        title="首页"
      >
        ⟪
      </button>
      
      <button
        @click="currentPage--"
        :disabled="currentPage === 1"
        class="page-btn"
        title="上一页"
      >
        ⟨
      </button>

      <template v-for="page in visiblePages" :key="page">
        <button
          v-if="page !== '...'"
          @click="currentPage = typeof page === 'number' ? page : currentPage"
          :class="['page-btn', 'page-btn-desktop', { active: currentPage === page }]"
        >
          {{ page }}
        </button>
        <span v-else class="page-ellipsis page-ellipsis-desktop">...</span>
      </template>

      <!-- 移动端简化分页 -->
      <template v-for="page in mobileVisiblePages" :key="`mobile-${page}`">
        <button
          @click="currentPage = typeof page === 'number' ? page : currentPage"
          :class="['page-btn', 'page-btn-mobile', { active: currentPage === page }]"
        >
          {{ page }}
        </button>
      </template>

      <button
        @click="currentPage++"
        :disabled="currentPage === totalPages"
        class="page-btn"
        title="下一页"
      >
        ⟩
      </button>
      
      <button
        @click="currentPage = totalPages"
        :disabled="currentPage === totalPages"
        class="page-btn"
        title="末页"
      >
        ⟫
      </button>

      <div class="page-info">
        第 {{ currentPage }} / {{ totalPages }} 页
      </div>
    </div>

    <!-- 成员展示 -->
    <div class="nav-card">
      <div class="card-grid">
        <div
          v-for="member in paginatedMembers"
          :key="member.name"
          class="card-item"
        >
          <a :href="member.link" target="_blank" class="card-link">
            <div class="card-avatar">
              <img :src="member.img" :alt="member.name" />
            </div>
            <div class="card-content">
              <h3 class="card-title">{{ member.name }}</h3>
              <p class="card-desc">{{ member.desc }}</p>
              <div v-if="member.badge" class="card-badge" :class="`badge-${member.badgeType || 'default'}`">
                {{ member.badge }}
              </div>
            </div>
          </a>
        </div>
      </div>
    </div>

    <!-- 分页控件 -->
    <div v-if="totalPages > 1" class="pagination">
      <button
        @click="currentPage = 1"
        :disabled="currentPage === 1"
        class="page-btn"
        title="首页"
      >
        ⟪
      </button>
      
      <button
        @click="currentPage--"
        :disabled="currentPage === 1"
        class="page-btn"
        title="上一页"
      >
        ⟨
      </button>

      <template v-for="page in visiblePages" :key="page">
        <button
          v-if="page !== '...'"
          @click="currentPage = typeof page === 'number' ? page : currentPage"
          :class="['page-btn', 'page-btn-desktop', { active: currentPage === page }]"
        >
          {{ page }}
        </button>
        <span v-else class="page-ellipsis page-ellipsis-desktop">...</span>
      </template>

      <!-- 移动端简化分页 -->
      <template v-for="page in mobileVisiblePages" :key="`mobile-${page}`">
        <button
          @click="currentPage = typeof page === 'number' ? page : currentPage"
          :class="['page-btn', 'page-btn-mobile', { active: currentPage === page }]"
        >
          {{ page }}
        </button>
      </template>

      <button
        @click="currentPage++"
        :disabled="currentPage === totalPages"
        class="page-btn"
        title="下一页"
      >
        ⟩
      </button>
      
      <button
        @click="currentPage = totalPages"
        :disabled="currentPage === totalPages"
        class="page-btn"
        title="末页"
      >
        ⟫
      </button>

      <div class="page-info">
        第 {{ currentPage }} / {{ totalPages }} 页
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface Member {
  name: string
  desc: string
  link: string
  img: string
  badge?: string
  badgeType?: string
}

const props = defineProps<{
  membersData: Member[]
}>()

// 响应式数据
const searchQuery = ref('')
const selectedGroup = ref('')
const currentPage = ref(1)
const pageSize = ref(20)

// 解析成员数据中的组别信息
const uniqueGroups = computed(() => {
  return ["管理组","策划组","美术组","音乐组","剪辑组","文案组"]
})

// 计算属性：过滤后的成员
const filteredMembers = computed(() => {
  let filtered = props.membersData

  // 按搜索关键词过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(member =>
      member.name.toLowerCase().includes(query) ||
      member.desc.toLowerCase().includes(query)
    )
  }

  // 按组别过滤
  if (selectedGroup.value) {
    filtered = filtered.filter(member =>
      member.desc.includes(selectedGroup.value)
    )
  }

  return filtered
})

// 计算属性：当前页的成员
const paginatedMembers = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredMembers.value.slice(start, end)
})

// 计算属性：总页数
const totalPages = computed(() => {
  return Math.ceil(filteredMembers.value.length / pageSize.value)
})

// 计算属性：可见的页码
const visiblePages = computed(() => {
  const current = currentPage.value
  const total = totalPages.value
  const delta = 2 // 当前页前后显示的页数

  let start = Math.max(1, current - delta)
  let end = Math.min(total, current + delta)

  // 调整范围以保持最多显示 5 个页码
  if (end - start < 4 && total > 5) {
    if (start === 1) {
      end = Math.min(total, start + 4)
    } else if (end === total) {
      start = Math.max(1, end - 4)
    }
  }

  const pages: (number | string)[] = []
  
  // 添加页码
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  // 添加省略号和边界页码
  if (start > 1) {
    if (start > 2) {
      pages.unshift('...')
    }
    pages.unshift(1)
  }

  if (end < total) {
    if (end < total - 1) {
      pages.push('...')
    }
    pages.push(total)
  }

  return pages
})

// 计算属性：移动端简化的页码
const mobileVisiblePages = computed(() => {
  const current = currentPage.value
  const total = totalPages.value
  
  // 移动端只显示当前页和相邻页
  const pages: (number | string)[] = []
  
  // 只显示当前页前后各1页，最多3个页码
  const start = Math.max(1, current - 1)
  const end = Math.min(total, current + 1)
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

// 监听筛选条件变化，重置到第一页
watch([searchQuery, selectedGroup, pageSize], () => {
  currentPage.value = 1
})

// 监听总页数变化，确保当前页不超过范围
watch(totalPages, (newTotal) => {
  if (currentPage.value > newTotal && newTotal > 0) {
    currentPage.value = newTotal
  }
})

// 暴露给父组件的方法
defineExpose({
  resetPagination: () => {
    currentPage.value = 1
    searchQuery.value = ''
    selectedGroup.value = ''
  }
})
</script>

<style scoped>
.members-pagination {
  max-width: 100%;
  margin: 1.5rem auto;
  padding: 0 1rem;
}

.search-box {
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
  position: relative;
}

.search-input {
  width: 100%;
  max-width: 500px;
  padding: 0.875rem 1.25rem 0.875rem 3.25rem;
  border: 2px solid var(--vp-c-border);
  border-radius: 12px;
  font-size: 1rem;
  background: var(--vp-c-bg);
  color: var(--vp-c-text-1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-input::placeholder {
  color: var(--vp-c-text-3);
  font-weight: 400;
}

.search-input:focus {
  outline: none;
  border-color: var(--vp-c-brand-1);
  box-shadow: 0 0 0 4px var(--vp-c-brand-soft), 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.search-box::before {
  content: "";
  position: absolute;
  left: calc(50% - 230px);
  top: 50%;
  transform: translate(-50%, -50%);
  width: 18px;
  height: 18px;
  border: 2.5px solid var(--vp-c-text-3);
  border-radius: 50%;
  pointer-events: none;
  z-index: 1;
  transition: all 0.3s ease;
}

.search-box::after {
  content: "";
  position: absolute;
  left: calc(50% - 218px);
  top: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  width: 3px;
  height: 10px;
  background: var(--vp-c-text-3);
  border-radius: 2px;
  pointer-events: none;
  z-index: 1;
  transition: all 0.3s ease;
}

.search-input:focus + .search-box::before,
.search-input:focus + .search-box::after {
  color: var(--vp-c-brand-1);
  border-color: var(--vp-c-brand-1);
  background: var(--vp-c-brand-1);
}

.filter-box {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2.5rem;
  margin-bottom: 1.25rem;
  flex-wrap: wrap;
  padding: 1.25rem;
  background: linear-gradient(135deg, var(--vp-c-bg-soft) 0%, var(--vp-c-bg-alt) 100%);
  border-radius: 16px;
  border: 1px solid var(--vp-c-divider);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 0.875rem;
}

.filter-box label {
  font-size: 0.95rem;
  color: var(--vp-c-text-1);
  font-weight: 600;
  white-space: nowrap;
  letter-spacing: 0.025em;
}

.group-filter,
.page-size-selector {
  padding: 0.625rem 1rem;
  border: 2px solid var(--vp-c-border);
  border-radius: 8px;
  background: var(--vp-c-bg);
  color: var(--vp-c-text-1);
  font-size: 0.9rem;
  min-width: 130px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  font-weight: 500;
}

.group-filter:hover,
.page-size-selector:hover {
  border-color: var(--vp-c-brand-1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
}

.group-filter:focus,
.page-size-selector:focus {
  outline: none;
  border-color: var(--vp-c-brand-1);
  box-shadow: 0 0 0 3px var(--vp-c-brand-soft), 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stats {
  margin-bottom: 1.25rem;
  font-size: 0.95rem;
  color: var(--vp-c-text-2);
  text-align: center;
  padding: 0.875rem 1.25rem;
  background: linear-gradient(135deg, var(--vp-c-bg-alt) 0%, var(--vp-c-bg-soft) 100%);
  border-radius: 12px;
  border-left: 4px solid var(--vp-c-brand-1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  font-weight: 500;
}

.nav-card {
  margin-bottom: 2rem;
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
  gap: 1.25rem;
}

.card-item {
  border: 2px solid var(--vp-c-border);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, var(--vp-c-bg) 0%, var(--vp-c-bg-soft) 100%);
  min-height: 100px;
  display: flex;
  flex-direction: column;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.card-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--vp-c-brand-1), var(--vp-c-brand-2));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card-item:hover {
  border-color: var(--vp-c-brand-1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12), 0 4px 10px rgba(0, 0, 0, 0.08);
  transform: translateY(-4px);
}

.card-item:hover::before {
  opacity: 1;
}

.card-link {
  display: flex;
  align-items: flex-start;
  padding: 1.25rem;
  text-decoration: none;
  color: inherit;
  gap: 1.125rem;
  height: 100%;
}

.card-avatar {
  flex-shrink: 0;
  margin-top: 0.125rem;
  position: relative;
}

.card-avatar::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--vp-c-brand-1), var(--vp-c-brand-2));
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.card-item:hover .card-avatar::after {
  opacity: 0.2;
}

.card-avatar img {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--vp-c-bg);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card-item:hover .card-avatar img {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-content {
  flex: 1;
  text-align: left;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.card-title {
  font-size: 1.05rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: var(--vp-c-text-1);
  line-height: 1.3;
  word-break: break-word;
  letter-spacing: 0.025em;
  transition: color 0.3s ease;
}

.card-item:hover .card-title {
  color: var(--vp-c-brand-1);
}

.card-desc {
  font-size: 0.85rem;
  color: var(--vp-c-text-2);
  margin: 0 0 0.75rem 0;
  line-height: 1.5;
  flex: 1;
  word-break: break-word;
  font-weight: 400;
}

.card-badge {
  display: inline-block;
  padding: 0.3rem 0.6rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  white-space: nowrap;
  margin-top: auto;
  align-self: flex-start;
  letter-spacing: 0.025em;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.badge-tip {
  background: linear-gradient(135deg, var(--vp-c-tip-soft), var(--vp-c-tip-softer));
  color: var(--vp-c-tip-darker);
  border: 1px solid var(--vp-c-tip-soft);
}

.card-item:hover .card-badge {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.875rem;
  margin: 2rem 0 1.5rem 0;
  flex-wrap: wrap;
  padding: 1.5rem;
  background: linear-gradient(135deg, var(--vp-c-bg-soft) 0%, var(--vp-c-bg-alt) 100%);
  border-radius: 16px;
  border: 1px solid var(--vp-c-divider);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
}

.pagination-top {
  margin: 1rem 0 1.25rem 0;
  padding: 1rem;
  background: linear-gradient(135deg, var(--vp-c-bg-alt) 0%, var(--vp-c-bg-soft) 100%);
  border: 1px solid var(--vp-c-border);
  border-radius: 12px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.04);
}

.page-btn {
  padding: 0.75rem 1rem;
  border: 2px solid var(--vp-c-border);
  border-radius: 8px;
  background: var(--vp-c-bg);
  color: var(--vp-c-text-1);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 0.9rem;
  min-width: 48px;
  font-weight: 600;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.page-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.page-btn:hover:not(:disabled) {
  border-color: var(--vp-c-brand-1);
  background: var(--vp-c-brand-soft);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.page-btn:hover:not(:disabled)::before {
  left: 100%;
}

.page-btn.active {
  background: linear-gradient(135deg, var(--vp-c-brand-1), var(--vp-c-brand-2));
  color: white;
  border-color: var(--vp-c-brand-1);
  box-shadow: 0 4px 12px var(--vp-c-brand-soft), 0 2px 4px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.page-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
}

.page-ellipsis {
  padding: 0.75rem 0.5rem;
  color: var(--vp-c-text-2);
  font-weight: 600;
  font-size: 0.9rem;
}

.page-info {
  margin-left: 1.5rem;
  font-size: 0.9rem;
  color: var(--vp-c-text-1);
  font-weight: 600;
  padding: 0.75rem 1.25rem;
  background: linear-gradient(135deg, var(--vp-c-bg) 0%, var(--vp-c-bg-alt) 100%);
  border-radius: 8px;
  border: 2px solid var(--vp-c-border);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  letter-spacing: 0.025em;
}

/* 默认显示桌面版分页，隐藏移动版 */
.page-btn-mobile {
  display: none;
}

.page-btn-desktop,
.page-ellipsis-desktop {
  display: inline-block;
}

/* 大屏幕布局优化 */
@media (min-width: 1200px) {
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    max-width: 1000px;
    margin: 0 auto;
  }

  .search-box::before {
    left: calc(50% - 200px);
  }

  .search-box::after {
    left: calc(50% - 188px);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-box::before {
    left: calc(50% - 200px);
  }

  .search-box::after {
    left: calc(50% - 188px);
  }

  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
  }

  .card-item {
    min-height: 95px;
    border-radius: 12px;
  }

  .card-link {
    padding: 1rem;
    gap: 1rem;
  }

  .card-avatar {
    margin-top: 0.1rem;
  }

  .card-avatar img {
    width: 50px;
    height: 50px;
  }

  .card-title {
    font-size: 1rem;
    margin-bottom: 0.4rem;
  }

  .card-desc {
    font-size: 0.8rem;
  }

  .filter-box {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .filter-item {
    justify-content: space-between;
  }

  .pagination {
    gap: 0.5rem;
    padding: 1rem;
    margin: 1.5rem 0 1rem 0;
  }

  .pagination-top {
    margin: 0.75rem 0 1rem 0;
    padding: 0.875rem;
  }

  .page-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
    min-width: 40px;
  }

  .page-info {
    margin-left: 0.75rem;
    font-size: 0.85rem;
    padding: 0.5rem 1rem;
  }
}

@media (max-width: 480px) {
  .members-pagination {
    padding: 0 0.5rem;
    margin: 1rem auto;
  }

  .search-box {
    margin-bottom: 0.875rem;
  }

  .search-box::before {
    left: calc(50% - 120px);
    width: 14px;
    height: 14px;
  }

  .search-box::after {
    left: calc(50% - 110px);
    height: 6px;
  }

  .search-input {
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    font-size: 0.9rem;
    border-radius: 10px;
  }

  .filter-box {
    gap: 1rem;
    padding: 1rem;
    flex-direction: column;
    align-items: stretch;
    border-radius: 12px;
  }

  .filter-item {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .filter-box label {
    font-size: 0.9rem;
    text-align: center;
    font-weight: 600;
  }

  .group-filter,
  .page-size-selector {
    width: 100%;
    padding: 0.75rem;
    font-size: 0.85rem;
    border-radius: 8px;
  }

  .stats {
    margin-bottom: 1rem;
    padding: 0.75rem;
    font-size: 0.9rem;
    border-radius: 10px;
  }

  .card-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .card-item {
    border-radius: 12px;
    min-height: 90px;
  }

  .card-link {
    padding: 0.875rem;
    gap: 0.875rem;
  }

  .card-avatar img {
    width: 48px;
    height: 48px;
  }

  .card-title {
    font-size: 0.95rem;
    margin-bottom: 0.375rem;
  }

  .card-desc {
    font-size: 0.75rem;
    margin-bottom: 0.5rem;
  }

  .card-badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    border-radius: 5px;
  }

  .pagination {
    gap: 0.5rem;
    padding: 1rem;
    margin: 1.5rem 0 1rem 0;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    border-radius: 12px;
  }

  .pagination-top {
    margin: 0.75rem 0 1rem 0;
    padding: 0.875rem;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    border-radius: 10px;
  }

  /* 隐藏桌面版分页，显示移动版 */
  .page-btn-desktop,
  .page-ellipsis-desktop {
    display: none;
  }

  .page-btn-mobile {
    display: inline-block;
  }

  .page-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    min-width: 36px;
    flex-shrink: 0;
    border-radius: 6px;
  }

  .page-info {
    width: 100%;
    margin: 0.75rem 0 0 0;
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
    text-align: center;
    order: 10;
    background: linear-gradient(135deg, var(--vp-c-bg) 0%, var(--vp-c-bg-alt) 100%);
    border-radius: 6px;
    border: 1px solid var(--vp-c-border);
  }
}
</style>
